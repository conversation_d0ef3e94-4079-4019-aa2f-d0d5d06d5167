import { vi } from 'vitest';
import type { User } from '../../types';
import { createMockUser } from '../utils';

export const authService = {
  getCurrentUser: vi.fn((): User | null => createMockUser()),
  onAuthStateChange: vi.fn((callback) => {
    callback(createMockUser());
    return vi.fn(); // unsubscribe function
  }),
  isInitialized: vi.fn(() => true),
  signIn: vi.fn(),
  signOut: vi.fn(),
  persistSession: vi.fn(),
  loadPersistedSession: vi.fn(),
  clearPersistedSession: vi.fn(),
};