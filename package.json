{"name": "mobilify-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "cypress:open": "cypress open", "cypress:run": "cypress run", "cypress:run:headless": "cypress run --headless", "e2e": "cypress run --spec 'cypress/e2e/**/*.cy.ts'", "e2e:open": "cypress open --e2e", "component:test": "cypress run --component", "component:open": "cypress open --component", "test:all": "npm run test:coverage && npm run cypress:run", "type-check": "tsc --noEmit"}, "dependencies": {"@sentry/react": "^10.0.0", "@sentry/vite-plugin": "^4.0.2", "autoprefixer": "^10.4.21", "date-fns": "^4.1.0", "firebase": "^10.12.2", "lucide-react": "^0.526.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.24.0", "tailwindcss": "^3.4.4"}, "devDependencies": {"@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "prettier": "^3.6.2", "terser": "^5.43.1", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vitest": "^3.2.4", "wait-on": "^8.0.4"}}