{"indexes": [{"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "restaurantId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "restaurantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "menuItems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "restaurantId", "order": "ASCENDING"}, {"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "displayOrder", "order": "ASCENDING"}]}, {"collectionGroup": "customers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "restaurantId", "order": "ASCENDING"}, {"fieldPath": "totalSpent", "order": "DESCENDING"}]}, {"collectionGroup": "reservations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "restaurantId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}, {"fieldPath": "time", "order": "ASCENDING"}]}], "fieldOverrides": []}