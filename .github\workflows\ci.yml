name: CI/CD Pipeline

on:
  push:
    branches: [ main, master, develop, phase-8 ]
  pull_request:
    branches: [ main, master ]

env:
  NODE_VERSION: '20.x'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Install dependencies
      run: npm ci

    - name: Type check
      run: npm run type-check

    - name: Lint
      run: npm run lint

    - name: Format check
      run: npm run format:check

    - name: Run unit tests
      run: npm run test:coverage

    - name: Run security tests
      run: npm test -- src/test/security/security.test.ts --run

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

    - name: Build application
      run: npm run build

    - name: Start dev server for E2E tests
      run: |
        npm run dev &
        npx wait-on http://localhost:5173

    - name: Run E2E tests
      run: |
        # Create directories for Cypress artifacts
        mkdir -p cypress/screenshots cypress/videos
        npm run cypress:run:headless

    - name: Upload E2E test artifacts
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: cypress-artifacts
        path: |
          cypress/screenshots
          cypress/videos
        if-no-files-found: ignore

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Install dependencies
      run: npm ci

    - name: Run npm audit
      run: npm audit --audit-level moderate

    - name: Run Snyk security scan
      uses: snyk/actions/node@master
      continue-on-error: true
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --file=package.json --severity-threshold=high

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test, security-scan]
    if: github.ref == 'refs/heads/phase-8' || github.ref == 'refs/heads/develop'
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Install dependencies
      run: npm ci

    - name: Build for staging
      run: npm run build
      env:
        VITE_FIREBASE_API_KEY: ${{ secrets.STAGING_FIREBASE_API_KEY }}
        VITE_FIREBASE_AUTH_DOMAIN: ${{ secrets.STAGING_FIREBASE_AUTH_DOMAIN }}
        VITE_FIREBASE_PROJECT_ID: ${{ secrets.STAGING_FIREBASE_PROJECT_ID }}
        VITE_FIREBASE_STORAGE_BUCKET: ${{ secrets.STAGING_FIREBASE_STORAGE_BUCKET }}
        VITE_FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.STAGING_FIREBASE_MESSAGING_SENDER_ID }}
        VITE_FIREBASE_APP_ID: ${{ secrets.STAGING_FIREBASE_APP_ID }}
        VITE_ENVIRONMENT: staging

    - name: Deploy to Vercel (Staging)
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        working-directory: ./
        alias-domains: mobilify-staging.vercel.app

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [test, security-scan]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    environment: production

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Install dependencies
      run: npm ci

    - name: Build for production
      run: npm run build
      env:
        VITE_FIREBASE_API_KEY: ${{ secrets.PROD_FIREBASE_API_KEY }}
        VITE_FIREBASE_AUTH_DOMAIN: ${{ secrets.PROD_FIREBASE_AUTH_DOMAIN }}
        VITE_FIREBASE_PROJECT_ID: ${{ secrets.PROD_FIREBASE_PROJECT_ID }}
        VITE_FIREBASE_STORAGE_BUCKET: ${{ secrets.PROD_FIREBASE_STORAGE_BUCKET }}
        VITE_FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.PROD_FIREBASE_MESSAGING_SENDER_ID }}
        VITE_FIREBASE_APP_ID: ${{ secrets.PROD_FIREBASE_APP_ID }}
        VITE_ENVIRONMENT: production
        VITE_SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
        VITE_GA_MEASUREMENT_ID: ${{ secrets.GA_MEASUREMENT_ID }}

    - name: Deploy to Vercel (Production)
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        working-directory: ./
        vercel-args: '--prod'
        alias-domains: mobilify-admin.vercel.app

    - name: Deploy Firebase Rules (Production)
      run: |
        npm install -g firebase-tools
        firebase use mobilify-pro-admin --token ${{ secrets.FIREBASE_TOKEN }}
        firebase deploy --only firestore:rules,storage:rules --token ${{ secrets.FIREBASE_TOKEN }}

    - name: Notify Sentry of deployment
      uses: getsentry/action-release@v1
      continue-on-error: true
      env:
        SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
        SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
        SENTRY_PROJECT: ${{ secrets.SENTRY_PROJECT }}
      with:
        environment: production
        version: ${{ github.sha }}

  backup:
    name: Backup Database
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    needs: deploy-production

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ secrets.GCP_PROJECT_ID }}

    - name: Export Firestore data
      run: |
        gcloud firestore export gs://${{ secrets.BACKUP_BUCKET }}/backups/$(date +%Y-%m-%d-%H-%M-%S) \
          --project=mobilify-pro-admin \
          --async
