rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow authenticated users to read and write all documents
    // This is for development - use more restrictive rules in production
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
    
    // Alternative: More specific rules for each collection
    // Uncomment these and comment out the above rule for production
    
    // // Orders collection - restaurant-specific access
    // match /orders/{orderId} {
    //   allow read, write: if request.auth != null 
    //     && request.auth.token.restaurantId == resource.data.restaurantId;
    // }
    
    // // Menu items collection - restaurant-specific access  
    // match /menuItems/{itemId} {
    //   allow read, write: if request.auth != null
    //     && request.auth.token.restaurantId == resource.data.restaurantId;
    // }
    
    // // Categories collection - restaurant-specific access
    // match /categories/{categoryId} {
    //   allow read, write: if request.auth != null
    //     && request.auth.token.restaurantId == resource.data.restaurantId;
    // }
    
    // // Reservations collection - restaurant-specific access
    // match /reservations/{reservationId} {
    //   allow read, write: if request.auth != null
    //     && request.auth.token.restaurantId == resource.data.restaurantId;
    // }
    
    // // Users collection - user can only access their own data
    // match /users/{userId} {
    //   allow read, write: if request.auth != null && request.auth.uid == userId;
    // }
  }
}
